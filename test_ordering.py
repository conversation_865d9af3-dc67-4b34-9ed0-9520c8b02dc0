#!/usr/bin/env python3
"""
Quick test to verify that the distribution ordering works correctly.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from persona_topic.distribution import RiskFocusedDistribution
from persona_topic.models import DataGenSource, DataGenMode

def test_ordering():
    """Test that KNOWLEDGE_GRAPH_SYSTEM_PROMPT comes last in the distribution."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    print("Distribution order:")
    for i, (source, count) in enumerate(distribution):
        print(f"{i+1}. {source.value}: {count}")
    
    # Check that KNOWLEDGE_GRAPH_SYSTEM_PROMPT is last if present
    kg_sources = [i for i, (source, count) in enumerate(distribution) 
                  if source == DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT]
    
    if kg_sources:
        kg_index = kg_sources[0]
        print(f"\nKNOWLEDGE_GRAPH_SYSTEM_PROMPT is at position {kg_index + 1} out of {len(distribution)}")
        assert kg_index == len(distribution) - 1, f"KNOWLEDGE_GRAPH_SYSTEM_PROMPT should be last, but it's at position {kg_index + 1}"
        print("✅ KNOWLEDGE_GRAPH_SYSTEM_PROMPT is correctly positioned last!")
    else:
        print("ℹ️  KNOWLEDGE_GRAPH_SYSTEM_PROMPT not present in distribution")

if __name__ == "__main__":
    test_ordering()
