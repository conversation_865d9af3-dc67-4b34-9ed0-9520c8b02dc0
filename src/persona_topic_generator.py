import asyncio
import json
import os
import random
from dataclasses import dataclass, field
from time import time, strftime
from typing import Callable, Dict, List, Optional, TypeVar

# Why are we using two different HTTP clients?
import boto3
import httpx
from schemas.experiments import DocumentReferences, SourceData
from schemas.generation import GenerateArgs, GenerationMethods
from settings import settings
from utils.logger import logger
from utils.control_plane import patch_experiment_persona_topics
from persona_topic import (
    PersonaTopic,
    DataGenMode,
    DataGenSource,
    DistributionStrategyFactory,
    DistributionStrategy,
    PersonaTopicGeneratorFactory,
    read_documents_for_experiment,
)
from persona_topic.utils import achat_completion
from persona_topic.knowledge_graph_system_prompt.models import KnowledgeGraph

T = TypeVar("T")

# Dataclass definitions for cleaner code structure
@dataclass
class TestMessage:
    """Represents the body of a test message sent to SQS."""
    role: str
    persona: str
    topic: str
    experiment_id: str
    source_tactics: List[Dict[str, List[str] | str]]
    tactics: List[str]
    generation_method: str
    risk_type: Optional[str]
    current_depth: int
    max_depth: int
    is_adapted_conversation: bool

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization."""
        return {
            "role": self.role,
            "persona": self.persona,
            "topic": self.topic,
            "experiment_id": self.experiment_id,
            "source_tactics": self.source_tactics,
            "tactics": self.tactics,
            "generation_method": self.generation_method,
            "risk_type": self.risk_type,
            "current_depth": self.current_depth,
            "max_depth": self.max_depth,
            "is_adapted_conversation": self.is_adapted_conversation,
        }


@dataclass
class MessageAttributes:
    """Represents SQS message attributes."""
    auth_header: Dict[str, str]

    def to_dict(self) -> Dict:
        """Convert to SQS message attributes format."""
        return {
            "Auth": {
                "DataType": "String",
                "StringValue": json.dumps(self.auth_header),
            }
        }


@dataclass
class GenerationConfiguration:
    """Represents generation configuration extracted from source data."""
    max_topics: int
    max_personas: int
    max_conversations: int = -1
    min_conversation_length: int = 2
    max_conversation_length: int = 7
    branching_factor: int = 1
    persona_topic_generators: Dict = field(default_factory=dict)
    intent: str = "general usage"
    data_gen_mode: Optional[DataGenMode] = None

    @classmethod
    def from_dict(cls, config_dict: Dict, settings_fallback) -> 'GenerationConfiguration':
        """Create GenerationConfiguration from dictionary with fallbacks to settings."""
        data_gen_mode = config_dict.get("data_gen_mode", settings_fallback.data_gen_mode)
        if isinstance(data_gen_mode, str):
            data_gen_mode = DataGenMode(data_gen_mode)

        return cls(
            max_topics=config_dict.get("max_topics") or settings_fallback.max_topics,
            max_personas=config_dict.get("max_personas") or settings_fallback.max_personas,
            max_conversations=config_dict.get("max_conversations", -1),
            min_conversation_length=config_dict.get("min_conversation_length", 2),
            max_conversation_length=config_dict.get("max_conversation_length", 7),
            branching_factor=config_dict.get("branching_factor", 1),
            persona_topic_generators=config_dict.get("persona_topic_generators", {}),
            intent=config_dict.get("intent", "general usage"),
            data_gen_mode=data_gen_mode,
        )


@dataclass
class TelemetryMetadata:
    """Represents telemetry metadata for tracking."""
    org_id: str
    user_id: str
    experiment_id: str

    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary for compatibility with existing code."""
        return {
            "org_id": self.org_id,
            "user_id": self.user_id,
            "experiment_id": self.experiment_id,
        }


@dataclass
class GenerationStats:
    """Represents generation statistics by source."""
    stats: Dict[DataGenSource, float] = field(default_factory=dict)

    def add_stat(self, source: DataGenSource, duration: float):
        """Add a generation time statistic."""
        self.stats[source] = duration

    def log_stats(self):
        """Log all generation statistics."""
        for source, duration in self.stats.items():
            logger.info(f"Generation time for {source}: {duration:.2f} seconds")


CONTROL_PLANE_URL = settings.control_plane_url
logger.info(f"Control plane URL: {CONTROL_PLANE_URL}")


class PersonaTopicGenerator:
    def __init__(self):
        options = {}
        sqs_region = os.environ.get(
            "SQS_REGION", os.environ.get("AWS_REGION", "us-east-1")
        )
        if sqs_region == "elasticmq":
            sqs_endpoint = os.environ.get("SQS_ENDPOINT", None)

            options["endpoint_url"] = sqs_endpoint
            options["region_name"] = sqs_region
            options["aws_secret_access_key"] = "x"
            options["aws_access_key_id"] = "x"
            options["use_ssl"] = False

        session = boto3.Session()
        self.sqs = session.client("sqs", **options)
        tests_queue_url = os.environ.get("GEN_TESTS_QUEUE_URL")
        logger.dev(f"tests_queue_url: {tests_queue_url}")
        if not tests_queue_url:
            raise ValueError("GEN_TESTS_QUEUE_URL is not set!")
        self.tests_queue_url = tests_queue_url

    async def async_run(self, callable: Callable[..., T], *args) -> T:
        semaphore = asyncio.Semaphore(1)
        loop = asyncio.get_event_loop()
        async with semaphore:
            fut = loop.run_in_executor(None, callable, *args)
            return await fut

    async def async_run_with_limit(self, callable: Callable[..., T], *args) -> T:
        return await self.async_run(callable, *args)

    def send_test_messages(
        self,
        *,
        generation_method: str,
        branching_factor: int,
        role: str,
        persona: str,
        topic: str,
        experiment_id: str,
        source_tactics: List[Dict[str, List[str] | str]] = [],
        tactics: List[str],
        auth_header: Dict[str, str],
        risk_type: Optional[str] = None,
        disable_probability: bool = False,
        current_depth: int,
        min_depth: int,
        max_depth: int,
        is_adapted_conversation: bool,
    ) -> int:
        num_messages = 0
        sampled_max_depth = random.randint(min_depth, max_depth)

        # Create message attributes once
        message_attributes = MessageAttributes(auth_header=auth_header)

        # TODO: Swap the if/else blocks and use a positive condition
        if generation_method != GenerationMethods.persona:
            for _ in range(0, branching_factor):
                num_messages += 1
                message = TestMessage(
                    role=role,
                    persona=persona,
                    topic=topic,
                    experiment_id=experiment_id,
                    source_tactics=source_tactics,
                    tactics=tactics,
                    generation_method=generation_method,
                    risk_type=risk_type,
                    current_depth=current_depth,
                    max_depth=sampled_max_depth,
                    is_adapted_conversation=is_adapted_conversation,
                )
                self.sqs.send_message(
                    QueueUrl=self.tests_queue_url,
                    MessageBody=json.dumps(message.to_dict()),
                    MessageAttributes=message_attributes.to_dict(),
                )
        else:
            if disable_probability:
                num_messages += 1
                message = TestMessage(
                    role=role,
                    persona=persona,
                    topic=topic,
                    experiment_id=experiment_id,
                    source_tactics=source_tactics,
                    tactics=tactics,
                    generation_method=GenerationMethods.persona,
                    risk_type=risk_type,
                    current_depth=current_depth,
                    max_depth=sampled_max_depth,
                    is_adapted_conversation=is_adapted_conversation,
                )
                self.sqs.send_message(
                    QueueUrl=self.tests_queue_url,
                    MessageBody=json.dumps(message.to_dict()),
                    MessageAttributes=message_attributes.to_dict(),
                )
        return num_messages

    def queue_prompt_group_generation(
        self,
        *,
        experiment_id: str,
        role: str,
        risks: List[str],
        branching_factor: Optional[int],
        min_depth: int,
        max_depth: int,
        personas: List[str] = [],
        topics: List[str] = [],
        auth_header: Dict[str, str] = {},
        disable_probability: bool = False,
        # Used for persona specific topics (risk based generation)
        generation_source: Optional[str] = None,
        persona_topics: Optional[list[PersonaTopic]] = [],
        max_conversations: int = -1,
        is_adapted_conversation: bool,
        messages: int = 0,
    ):
        # load tactics from tactics/manual.tsv
        # and parse into List[str]
        tactics = []
        # Get the directory of the current script
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Construct the full path to the file
        file_path = os.path.join(script_dir, "tactics", "general.tsv")
        with open(file_path) as f:
            for line in f:
                tactics.append(line.strip().split("\t"))
        BRANCHING_FACTOR = branching_factor

        assert BRANCHING_FACTOR is not None, "Branching factor is required"

        test_count = (
            len(persona_topics) if persona_topics else len(personas) * len(topics)
        )
        logger.info(
            f"*** Queuing {test_count} tests for experiment {experiment_id} ***"
        )
        if persona_topics:
            for persona_topic in persona_topics:
                if max_conversations > 0 and messages >= max_conversations:
                    break
                generation_method = GenerationMethods.manyturn
                if (
                    persona_topic.generation_source
                    == DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT
                ):
                    generation_method = GenerationMethods.magpie
                num_messages = self.send_test_messages(
                    generation_method=generation_method,
                    branching_factor=BRANCHING_FACTOR,
                    role=role,
                    persona=persona_topic.persona,
                    topic=persona_topic.topic,
                    experiment_id=experiment_id,
                    source_tactics=persona_topic.tactics,
                    tactics=[],
                    auth_header=auth_header,
                    disable_probability=disable_probability,
                    risk_type=persona_topic.risk_type,
                    current_depth=0,
                    min_depth=min_depth,
                    max_depth=max_depth,
                    is_adapted_conversation=is_adapted_conversation,
                )
                messages += num_messages

        else:
            for persona in personas:
                for topic in topics:
                    if max_conversations > 0 and messages >= max_conversations:
                        break

                    if generation_source == DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT:
                        generation_method = GenerationMethods.magpie
                    else:
                        generation_method = GenerationMethods.manyturn

                    num_messages = self.send_test_messages(
                        generation_method=generation_method,
                        branching_factor=BRANCHING_FACTOR,
                        role=role,
                        persona=persona,
                        topic=topic,
                        experiment_id=experiment_id,
                        tactics=[],
                        source_tactics=[],
                        auth_header=auth_header,
                        disable_probability=disable_probability,
                        current_depth=0,
                        min_depth=min_depth,
                        max_depth=max_depth,
                        is_adapted_conversation=is_adapted_conversation,
                    )
                    messages += num_messages
        return messages

    async def generate(self, args: GenerateArgs):
        start_time = time()
        # Extract basic args - no more vars() and dictionary access
        role = args.role
        experiment_id = args.experiment_id
        auth_header = args.auth_header
        dry_run = args.dry_run
        try:
            ############################################################
            #### Unpack args
            ############################################################
            logger.dev(f"dry_run: {dry_run}")
            CONTROL_PLANE_URL = settings.control_plane_url
            if dry_run:
                CONTROL_PLANE_URL = "http://localhost:48001/cache"
                settings.persona_topic_settings.historical_document_store = "local"
                settings.persona_topic_settings.topic_document_store = "local"

            org_id = "invalid"
            user_id = "invalid"
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{CONTROL_PLANE_URL}/api/users/me",
                    headers=auth_header,
                )
                if response.status_code != 200:
                    logger.error(
                        f"Failed to fetch organization ID. Status code: {response.status_code}, Response: {response.text}"
                    )
                    raise Exception("Failed to fetch user organization ID.")
                user = response.json()
                organizations = user["organizations"]
                org_id = organizations[0].get("id", "invalid")
                user_id = user.get("id", "invalid")

            # Handle source_data properly - no more manual dict conversion
            source_data_typed = args.source_data
            if not isinstance(source_data_typed, SourceData):
                # Convert if needed, but this should rarely happen with proper typing
                source_data_dict = source_data_typed if isinstance(source_data_typed, dict) else source_data_typed.model_dump()
                source_data_typed = SourceData(
                    docs=DocumentReferences(**source_data_dict.get("docs", {})),
                    generation_configuration=source_data_dict.get("generation_configuration", {}),
                    evaluation_configuration=source_data_dict.get("evaluation_configuration", {}),
                )

            # Use dataclass for cleaner configuration handling
            generation_config = GenerationConfiguration.from_dict(
                source_data_typed.generation_configuration or {},
                settings.persona_topic_settings
            )

            logger.dev(f"===> Max Personas: {generation_config.max_personas}")
            logger.dev(f"===> Max Topics: {generation_config.max_topics}")

            evaluation_configuration = source_data_typed.evaluation_configuration or {}
            risks = [k for k in evaluation_configuration.values()]

            data_gen_mode_majority_fraction = (
                settings.persona_topic_settings.data_gen_mode_majority_fraction
            )
            historical_data_provided = (
                source_data_typed.docs.historical_data is not None
                and source_data_typed.docs.historical_data != []
            )
            knowledge_base_provided = (
                source_data_typed.docs.knowledge_base is not None
                and source_data_typed.docs.knowledge_base != []
            )

            async with httpx.AsyncClient() as client:
                logger.dev(
                    f"patching TO {CONTROL_PLANE_URL}/api/experiments/{experiment_id}"
                )
                response = await client.patch(
                    f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}",
                    json={"generation_status": "in progress"},
                    headers=auth_header,
                )
                logger.dev(f"response: {response}")
                logger.dev(f"response text: {response.text}")

            skip_risks = [
                "Hallucination",
                "Assistant Quality",
                "Sounds like a Bot",
                "Detect Successful Jailbreaking",
            ]
            risks = [r for r in risks if r["name"] not in skip_risks]

            # Split max personas param into different data sources using DistributionStrategy
            distribution_strategy = DistributionStrategyFactory.get_strategy(
                data_gen_mode=generation_config.data_gen_mode,
            )

            distribution = distribution_strategy.distribute(
                max_personas=generation_config.max_personas,
                historical_data_present=historical_data_provided,
                knowledge_base_present=knowledge_base_provided,
                num_risks=len(risks),
                n_historical_data_pts=len(source_data_typed.docs.historical_data or []),
                majority_fraction=data_gen_mode_majority_fraction,
                data_gen_mode=generation_config.data_gen_mode,
                custom_generator_configuration=generation_config.persona_topic_generators,
            )

            # Log distribution information
            logger.info(f"Persona distribution strategy: {generation_config.data_gen_mode}")
            logger.info(f"Total personas: {generation_config.max_personas}")
            DistributionStrategy.log_stats(distribution)

            ############################################################
            #### Generate personas and topics
            ############################################################

            all_persona_topics: list[PersonaTopic] = []
            historical_documents = []
            generation_stats = GenerationStats()
            telemetry_metadata = TelemetryMetadata(
                org_id=org_id,
                user_id=user_id,
                experiment_id=experiment_id,
            )
            
            # Read documents and load knowledge graph if needed
            kg_needed = distribution.get(DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT, 0) > 0
            knowledge_graph = None

            if kg_needed and source_data_typed.docs.knowledge_base is not None and source_data_typed.docs.knowledge_base != []:
                # Load KG from file if KNOWLEDGE_GRAPH_SYSTEM_PROMPT > 0
                from persona_topic.knowledge_graph_system_prompt.knowledge_graph import load_knowledge_graph_from_file

                # Hardcoded path for now
                kg_file_path = "/Users/<USER>/simlab/kb-gen-2/output/knowledge_graph_dry-run.json"
                logger.info(f"Loading knowledge graph from file: {kg_file_path}")

                # Load knowledge graph from file - will raise exception if it fails
                knowledge_graph = load_knowledge_graph_from_file(
                    file_path=kg_file_path
                )
                logger.info(f"Loaded knowledge graph with {knowledge_graph.entity_count} entities and {knowledge_graph.relationship_count} relationships")
            historical_needed = distribution.get(DataGenSource.HISTORICAL_DATA, 0) > 0
            if historical_needed and source_data_typed.docs.historical_data is not None and source_data_typed.docs.historical_data != []:
                historical_documents = read_documents_for_experiment(
                    source_data_typed.docs.historical_data,
                    org_id=org_id,
                    store=settings.persona_topic_settings.historical_document_store,
                    aws_s3_document_bucket=settings.persona_topic_settings.aws_s3_document_bucket,
                )
                
            # SYNC
            message_count = 0
            # Process all sources except KNOWLEDGE_GRAPH_SYSTEM_PROMPT first
            ordered_sources = []
            kg_source_data = None
            
            # Separate KNOWLEDGE_GRAPH_SYSTEM_PROMPT to process it last
            for source, count in distribution.items():
                if count <= 0:
                    continue
                    
                if source == DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT:
                    kg_source_data = (source, count)
                else:
                    ordered_sources.append((source, count))
                    
            # Add KNOWLEDGE_GRAPH_SYSTEM_PROMPT at the end if it exists
            if kg_source_data:
                ordered_sources.append(kg_source_data)
            all_personas = []
            # Process sources in the determined order
            for source, count in ordered_sources:
                custom_generator_settings = {}
                for custom_generator in generation_config.persona_topic_generators:
                    if custom_generator.get("name", "") == source.value:
                        custom_generator_settings = custom_generator.get("settings", {})
                        break

                generator = PersonaTopicGeneratorFactory.create_generator(
                    source,
                    custom_generator_settings,
                    metadata=telemetry_metadata.to_dict(),
                )

                start = time()
                persona_topics = await generator.generate(
                    role=role,
                    experiment_intent=generation_config.intent,
                    max_personas=count,
                    max_topics=custom_generator_settings.get("max_topics", generation_config.max_topics),
                    source_data=source_data_typed,
                    historical_data=historical_documents,
                    knowledge_graph=knowledge_graph,
                    existing_personas=all_personas,
                )
                end = time()
                generation_stats.add_stat(source, end - start)

                all_personas.extend([pt.persona for pt in persona_topics])
                message_count = self.queue_prompt_group_generation(
                    experiment_id=experiment_id,
                    role=role,
                    risks=risks,
                    branching_factor=generation_config.branching_factor,
                    auth_header=auth_header,
                    persona_topics=persona_topics,
                    max_conversations=generation_config.max_conversations,
                    is_adapted_conversation=False,
                    messages=message_count,
                    min_depth=generation_config.min_conversation_length,
                    max_depth=generation_config.max_conversation_length,
                )
                personas = [pt.persona for pt in persona_topics]
                topics = [pt.topic for pt in persona_topics]
                try:
                    await patch_experiment_persona_topics(
                        experiment_id=experiment_id,
                        personas=personas,
                        topics=topics,
                        persona_topics=[pt.model_dump() for pt in persona_topics],
                        CONTROL_PLANE_URL=CONTROL_PLANE_URL,
                        auth_header=auth_header,
                    )
                except Exception as e:
                    logger.error(
                        f"Error patching experiment {experiment_id}: {e} with {persona_topics}"
                    )
                    raise e
                all_persona_topics.extend(persona_topics)
                logger.info(f"==> Generated {len(persona_topics)} persona topics")
                logger.info(
                    f"==> Total {message_count} tests for experiment {experiment_id} for {source.value}"
                )
            # Collect all personas and topics
            all_personas = [pt.persona for pt in all_persona_topics]
            all_topics = [pt.topic for pt in all_persona_topics]
            logger.info(f"==> Generated {len(all_personas)} personas")

            # all_persona_summaries = await self.summarize_personas(all_personas)
            # print('all_persona_summaries', all_persona_summaries)

            persona_genmodes = list(
                set((pt.persona, pt.generation_source) for pt in all_persona_topics)
            )
            for pg in persona_genmodes:
                logger.info("==> Generated persona: " + pg[0] + " from " + pg[1].value)

            logger.info("end of persona topics")

            end_time = time()
            generation_stats.log_stats()
            logger.info(
                f"Total Generation Time: {end_time - start_time:.2f} seconds\n"
                f"Total Persona Topics: {len(all_persona_topics)}\n"
                f"Total Personas: {len(set(pt.persona for pt in all_persona_topics))}\n"
                f"Total Topics: {len(all_persona_topics)}\n"
            )

            ############################################################
            #### Save to DB and queue conversation generation
            ############################################################
            if dry_run:
                timestamp = strftime("%I-%M%p_%b%d")
                persona_output_path = f"{CONTROL_PLANE_URL}/ml/experiments/{experiment_id}/unique_personas_p-{generation_config.max_personas}_t-{generation_config.max_topics}_mode-{generation_config.data_gen_mode}_{timestamp}"
                output = {
                    "persona_topics": [
                        {
                            "persona": persona,
                            "generation_source": source,
                            "topics": list(topics),
                        }
                        for persona, source, topics in {
                            (
                                pt.persona,
                                pt.generation_source.value,
                                frozenset(
                                    [
                                        pt2.topic
                                        for pt2 in all_persona_topics
                                        if pt2.persona == pt.persona
                                        and pt2.generation_source
                                        == pt.generation_source
                                    ]
                                ),
                            )
                            for pt in all_persona_topics
                        }
                    ]
                }

                async with httpx.AsyncClient() as client:
                    await client.patch(
                        persona_output_path,
                        json=output,
                        headers=auth_header,
                    )
                logger.info(
                    f"==> Generated Personas: {json.dumps(all_personas, indent=2)}"
                )
                logger.info(
                    f"==> Generated Persona Topics: {json.dumps([{**pt.model_dump(), 'generation_source': pt.generation_source.value} for pt in all_persona_topics], indent=2)}"
                )

                logger.info(f"==> Number of Personas: {len(all_personas)}")
                logger.info(f"==>  Number of Persona Topics: {len(all_persona_topics)}")

                logger.info(f"==> Number of Topics: {len(all_topics)}")

                logger.info(
                    "\n================================================\n"
                    "Finished dry run\n"
                    "================================================\n"
                )

            await patch_experiment_persona_topics(
                experiment_id=experiment_id,
                # persona_summaries=all_persona_summaries,
                CONTROL_PLANE_URL=CONTROL_PLANE_URL,
                auth_header=auth_header,
            )
            end_time = time()
            # logger.info(
            #     f"=== generated {num_messages} tests in {end_time - start_time} seconds ==="
            # )
            logger.info("Waiting for tests to finish...")
            logger.info("Updating generation_status to completed...")
            async with httpx.AsyncClient() as client:
                await client.patch(
                    f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}",
                    json={"generation_status": "completed"},
                    headers=auth_header,
                )
            return all_persona_topics
        except Exception as e:
            logger.dev(f"Error generating prompts for experiment {experiment_id}")
            logger.error(e)
            import traceback

            traceback.print_exception(e)
            raise e

    async def summarize_personas(self, personas: List[str]) -> List[str]:
        """
        Generate concise 2-3 word summaries of each persona using AI, processing in batches of 10.

        Args:
            personas: List of persona descriptions

        Returns:
            List of 2-3 word summaries for each persona
        """
        # Create a prompt that instructs the AI to generate concise summaries
        prompt = """Given the following list of personas, generate a concise 2-3 word summary for each one that captures their key characteristics or role. Focus on their primary identity or function.

Example input:
"Tech-savvy Graduate Student: Jamie is a PhD candidate in computational linguistics who meticulously tracks research outputs across multiple platforms."

Example output:
"Research Student"

Now, generate 2-3 word summaries for these personas:

{personas}

Return only the list of summaries, one per line, without any additional text or formatting."""

        # Process personas in batches of 10
        batch_size = 5
        all_summaries = []

        for i in range(0, len(personas), batch_size):
            print(
                f"Processing batch {i // batch_size + 1} of {len(personas) // batch_size}"
            )
            batch = personas[i : i + batch_size]
            # Format the prompt with the current batch of personas
            formatted_prompt = prompt.format(personas="\n".join(batch))

            # Use AI to generate the summaries for this batch
            text = await achat_completion(
                model="claude-3-7-sonnet-20250219",
                messages=[{"role": "user", "content": formatted_prompt}],
                # metadata=self.metadata,
            )

            # Split the response into lines and clean up
            batch_summaries = [
                line.strip() for line in text.split("\n") if line.strip()
            ]

            # Ensure we have the same number of summaries as personas in this batch
            if len(batch_summaries) != len(batch):
                logger.warning(
                    f"Generated {len(batch_summaries)} summaries for {len(batch)} personas in batch {i // batch_size + 1}"
                )
                # If we got fewer summaries, pad with generic ones
                while len(batch_summaries) < len(batch):
                    batch_summaries.append("Generic User")
                # If we got more summaries, trim the list
                batch_summaries = batch_summaries[: len(batch)]

            all_summaries.extend(batch_summaries)

        return all_summaries


persona_topic_generator = PersonaTopicGenerator()
