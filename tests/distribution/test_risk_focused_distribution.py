from src.persona_topic.distribution import RiskFocusedDistribution

import math
from src.persona_topic.models import DataGenSource, DataGenMode


def test_risk_focused_distribution_basic():
    """Test basic distribution with standard parameters."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check that risks get majority allocation (75%)
    assert distribution[DataGenSource.RISKS_SELECTED] == 75

    # Check individual allocations
    remaining = max_personas - 75  # 25 personas
    # With historical_data weight=0.6, knowledge_base weight=0.2, app_description weight=0.2
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6
    )  # 15
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2
    )  # 5
    assert distribution[DataGenSource.APP_DESCRIPTION] == 5  # Remaining 5

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas


def test_risk_focused_distribution_no_historical_data():
    """Test distribution when historical data is not present."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=False,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check that historical data gets no allocation
    assert DataGenSource.HISTORICAL_DATA not in distribution

    # Check individual allocations
    assert distribution[DataGenSource.RISKS_SELECTED] == 75
    remaining = max_personas - 75  # 25 personas
    # With knowledge_base weight=0.2, app_description weight=0.2, total weight=0.4
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2 / 0.4
    )  # 12
    assert distribution[DataGenSource.APP_DESCRIPTION] == 13  # Remaining 13

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas


def test_risk_focused_distribution_no_knowledge_base():
    """Test distribution when knowledge base is not present."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=False,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check that knowledge graph system prompt gets no allocation
    assert DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT not in distribution

    # Check individual allocations
    assert distribution[DataGenSource.RISKS_SELECTED] == 75
    remaining = max_personas - 75  # 25 personas
    # With historical_data weight=0.6, app_description weight=0.2, total weight=0.8
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6 / 0.8
    )  # 18
    assert distribution[DataGenSource.APP_DESCRIPTION] == 7  # Remaining 7

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas


def test_risk_focused_distribution_no_data_sources():
    """Test distribution when no data sources are present."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=False,
        knowledge_base_present=False,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check individual allocations
    assert distribution[DataGenSource.RISKS_SELECTED] == 75
    assert distribution[DataGenSource.APP_DESCRIPTION] == 25

    # Check that only risks and app description get allocations
    assert len(distribution) == 2

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas


def test_risk_focused_distribution_edge_case_few_personas():
    """Test distribution when max_personas is very small."""
    strategy = RiskFocusedDistribution()
    max_personas = 10
    num_risks = 5

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check individual allocations
    assert distribution[DataGenSource.RISKS_SELECTED] == 8  # ceil(10 * 0.75) = 8
    assert distribution[DataGenSource.HISTORICAL_DATA] == 1  # floor(2 * 0.6) = 1
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == 0  # floor(2 * 0.2) = 0
    assert distribution[DataGenSource.APP_DESCRIPTION] == 1  # Remaining 1

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas


def test_risk_focused_distribution_edge_case_many_risks():
    """Test distribution when num_risks is greater than max_personas * majority_fraction."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 80  # More than 75% of max_personas

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check individual allocations
    assert (
        distribution[DataGenSource.RISKS_SELECTED] == 80
    )  # num_risks takes precedence
    remaining = max_personas - 80  # 20 personas
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6
    )  # 12
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2
    )  # 4
    assert distribution[DataGenSource.APP_DESCRIPTION] == 4  # Remaining 4

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas


def test_risk_focused_distribution_edge_case_risks_take_all():
    """Test distribution when risks need all available personas."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 100  # Equal to max_personas

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.75,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check individual allocations
    assert distribution[DataGenSource.RISKS_SELECTED] == 100  # All personas go to risks

    # Check that no other sources get allocations
    assert len(distribution) == 1

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas


def test_risk_focused_distribution_custom_majority_fraction():
    """Test distribution with a custom majority fraction."""
    strategy = RiskFocusedDistribution()
    max_personas = 100
    num_risks = 20
    custom_fraction = 0.6

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=custom_fraction,
        data_gen_mode=DataGenMode.RISK_FOCUSED,
    )

    # Check individual allocations
    assert distribution[DataGenSource.RISKS_SELECTED] == 60  # ceil(100 * 0.6) = 60
    remaining = max_personas - 60  # 40 personas
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6
    )  # 24
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2
    )  # 8
    assert distribution[DataGenSource.APP_DESCRIPTION] == 8  # Remaining 8

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas
