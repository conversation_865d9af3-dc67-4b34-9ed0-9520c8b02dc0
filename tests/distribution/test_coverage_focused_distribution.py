from src.persona_topic.distribution import CoverageFocusedDistribution

import math
from src.persona_topic.models import DataGenSource, DataGenMode


def test_coverage_focused_distribution_basic():
    """Test basic distribution with standard parameters."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
        data_gen_mode_majority_fraction=0.9,
    )

    # Check that risks get exactly num_risks allocation
    assert distribution[DataGenSource.RISKS_SELECTED] == num_risks

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas

    # Check that all sources are present
    assert DataGenSource.HISTORICAL_DATA in distribution
    assert DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT in distribution
    assert DataGenSource.APP_DESCRIPTION in distribution

    # Validate the specific values based on the distribution logic
    remaining = max_personas - num_risks  # 80 personas
    # With historical_data weight=0.6, knowledge_base weight=0.2, app_description weight=0.2
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6
    )  # 48
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2
    )  # 16
    assert distribution[DataGenSource.APP_DESCRIPTION] == remaining - math.floor(
        remaining * 0.6
    ) - math.floor(remaining * 0.2)  # 16


def test_coverage_focused_distribution_no_historical_data():
    """Test distribution when historical data is not present."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=False,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.9,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that historical data gets no allocation
    assert DataGenSource.HISTORICAL_DATA not in distribution

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas

    # Check that knowledge graph system prompt and app description get allocations
    assert DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT in distribution
    assert DataGenSource.APP_DESCRIPTION in distribution

    # Validate the specific values based on the distribution logic
    remaining = max_personas - num_risks  # 80 personas
    # With knowledge_base weight=0.2, app_description weight=0.2, total weight=0.4
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2 / 0.4
    )  # 40
    assert distribution[DataGenSource.APP_DESCRIPTION] == remaining - math.floor(
        remaining * 0.2 / 0.4
    )  # 40


def test_coverage_focused_distribution_no_knowledge_base():
    """Test distribution when knowledge base is not present."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=False,
        data_gen_mode_majority_fraction=0.9,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that knowledge graph system prompt gets no allocation
    assert DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT not in distribution

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas

    # Check that historical data and app description get allocations
    assert DataGenSource.HISTORICAL_DATA in distribution
    assert DataGenSource.APP_DESCRIPTION in distribution

    # Validate the specific values based on the distribution logic
    remaining = max_personas - num_risks  # 80 personas
    # With historical_data weight=0.6, app_description weight=0.2, total weight=0.8
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6 / 0.8
    )  # 60
    assert distribution[DataGenSource.APP_DESCRIPTION] == remaining - math.floor(
        remaining * 0.6 / 0.8
    )  # 20


def test_coverage_focused_distribution_no_data_sources():
    """Test distribution when no data sources are present."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 20

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=False,
        knowledge_base_present=False,
        data_gen_mode_majority_fraction=0.9,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that only risks and app description get allocations
    assert len(distribution) == 2
    assert DataGenSource.RISKS_SELECTED in distribution
    assert DataGenSource.APP_DESCRIPTION in distribution

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas

    # Validate the specific values
    assert distribution[DataGenSource.RISKS_SELECTED] == num_risks  # 20
    assert distribution[DataGenSource.APP_DESCRIPTION] == max_personas - num_risks  # 80


def test_coverage_focused_distribution_edge_case_few_personas():
    """Test distribution when max_personas is very small."""
    strategy = CoverageFocusedDistribution()
    max_personas = 10
    num_risks = 5

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.9,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that risks get exactly num_risks allocation
    assert distribution[DataGenSource.RISKS_SELECTED] == num_risks

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas

    # Validate the specific values
    remaining = max_personas - num_risks  # 5 personas
    # With historical_data weight=0.6, knowledge_base weight=0.2, app_description weight=0.2
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6
    )  # 3
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2
    )  # 1
    assert distribution[DataGenSource.APP_DESCRIPTION] == remaining - math.floor(
        remaining * 0.6
    ) - math.floor(remaining * 0.2)  # 1


def test_coverage_focused_distribution_edge_case_many_risks():
    """Test distribution when num_risks is close to max_personas."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 90

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.9,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that risks get exactly num_risks allocation
    assert distribution[DataGenSource.RISKS_SELECTED] == num_risks

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas

    # Validate the specific values
    remaining = max_personas - num_risks  # 10 personas
    # With historical_data weight=0.6, knowledge_base weight=0.2, app_description weight=0.2
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6
    )  # 6
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2
    )  # 2
    assert distribution[DataGenSource.APP_DESCRIPTION] == remaining - math.floor(
        remaining * 0.6
    ) - math.floor(remaining * 0.2)  # 2


def test_coverage_focused_distribution_edge_case_risks_take_all():
    """Test distribution when risks need all available personas."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 100  # Equal to max_personas

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.9,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that risks get all personas
    assert distribution[DataGenSource.RISKS_SELECTED] == max_personas

    # Check that no other sources get allocations
    assert len(distribution) == 1

    # Validate the specific value
    assert distribution[DataGenSource.RISKS_SELECTED] == 100


def test_coverage_focused_distribution_risks_exceed_max():
    """Test distribution when num_risks exceeds max_personas."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 120  # More than max_personas

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.9,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that risks get all personas
    assert distribution[DataGenSource.RISKS_SELECTED] == max_personas

    # Check that no other sources get allocations
    assert len(distribution) == 1

    # Validate the specific value
    assert distribution[DataGenSource.RISKS_SELECTED] == 100


def test_coverage_focused_distribution_custom_majority_fraction():
    """Test distribution with a custom majority fraction (should not affect coverage focused)."""
    strategy = CoverageFocusedDistribution()
    max_personas = 100
    num_risks = 20
    custom_fraction = 0.6

    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=num_risks,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=custom_fraction,
        data_gen_mode=DataGenMode.COVERAGE_FOCUSED,
    )

    # Check that risks get exactly num_risks allocation (not affected by fraction)
    assert distribution[DataGenSource.RISKS_SELECTED] == num_risks

    # Check that all personas are distributed
    assert sum(distribution.values()) == max_personas

    # Validate the specific values (should be the same as in the basic test)
    remaining = max_personas - num_risks  # 80 personas
    # With historical_data weight=0.6, knowledge_base weight=0.2, app_description weight=0.2
    assert distribution[DataGenSource.HISTORICAL_DATA] == math.floor(
        remaining * 0.6
    )  # 48
    assert distribution[DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT] == math.floor(
        remaining * 0.2
    )  # 16
    assert distribution[DataGenSource.APP_DESCRIPTION] == remaining - math.floor(
        remaining * 0.6
    ) - math.floor(remaining * 0.2)  # 16
